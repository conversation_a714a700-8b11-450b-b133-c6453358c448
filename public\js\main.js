// Modern FAQ Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('faqForm');
    const addQuestionBtn = document.getElementById('addQuestionBtn');
    const questionsContainer = document.getElementById('questionsContainer');
    const submitBtn = document.getElementById('submitBtn');
    const successMessage = document.getElementById('successMessage');

    let questionCount = 1;

    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add new question field with modern styling
    addQuestionBtn.addEventListener('click', function() {
        questionCount++;
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-item relative';
        questionDiv.style.opacity = '0';
        questionDiv.style.transform = 'translateY(20px)';
        questionDiv.innerHTML = `
            <textarea name="question" rows="4" required
                class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                placeholder="Enter your question about Winplus pharmacy management platform..."></textarea>
            <button type="button" class="remove-question absolute top-2 right-2 w-8 h-8 bg-red-100 text-red-600 rounded-full flex items-center justify-center hover:bg-red-200 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        `;

        questionsContainer.appendChild(questionDiv);

        // Animate in
        setTimeout(() => {
            questionDiv.style.opacity = '1';
            questionDiv.style.transform = 'translateY(0)';
        }, 50);

        // Add remove functionality
        const removeBtn = questionDiv.querySelector('.remove-question');
        removeBtn.addEventListener('click', function() {
            questionDiv.style.transform = 'scale(0.95)';
            questionDiv.style.opacity = '0';
            setTimeout(() => {
                questionDiv.remove();
                questionCount--;
                updateAddButtonVisibility();
            }, 200);
        });

        updateAddButtonVisibility();

        // Add animations to the new question item
        addQuestionItemAnimations();

        // Focus on the new textarea with animation
        const newTextarea = questionDiv.querySelector('textarea');
        setTimeout(() => {
            newTextarea.focus();
        }, 300);
    });

    // Update add button visibility (limit to 10 questions)
    function updateAddButtonVisibility() {
        if (questionCount >= 10) {
            addQuestionBtn.style.display = 'none';
        } else {
            addQuestionBtn.style.display = 'inline-flex';
        }
    }

    // Add visual feedback for form interactions
    const formInputs = form.querySelectorAll('input, textarea');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state with modern styling
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div><span>Submitting...</span>';
        submitBtn.disabled = true;
        submitBtn.style.opacity = '0.8';

        try {
            // Collect form data
            const formData = new FormData(form);
            const questions = [];
            
            // Get all question textareas
            const questionTextareas = form.querySelectorAll('textarea[name="question"]');
            questionTextareas.forEach(textarea => {
                if (textarea.value.trim()) {
                    questions.push(textarea.value.trim());
                }
            });

            if (questions.length === 0) {
                throw new Error('Please enter at least one question.');
            }

            const data = {
                user_name: formData.get('userName'),
                user_email: formData.get('userEmail'),
                user_phone: formData.get('userPhone'),
                user_company: formData.get('userCompany'),
                questions: questions
            };

            // Submit to server
            const response = await fetch('/api/questions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                // Show success message with animation
                successMessage.classList.remove('hidden');
                successMessage.style.opacity = '0';
                successMessage.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    successMessage.style.opacity = '1';
                    successMessage.style.transform = 'translateY(0)';
                }, 50);

                successMessage.scrollIntoView({ behavior: 'smooth' });

                // Reset form with animation
                form.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    form.reset();
                    form.style.transform = 'scale(1)';

                    // Reset questions container to single question
                    questionsContainer.innerHTML = `
                        <div class="question-item">
                            <textarea name="question" rows="4" required
                                class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                                placeholder="Enter your question about Winplus pharmacy management platform..."></textarea>
                        </div>
                    `;
                    questionCount = 1;
                    updateAddButtonVisibility();
                }, 200);

                // Hide success message after 8 seconds
                setTimeout(() => {
                    successMessage.style.opacity = '0';
                    successMessage.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        successMessage.classList.add('hidden');
                    }, 300);
                }, 8000);
            } else {
                throw new Error(result.message || 'Failed to submit questions');
            }
        } catch (error) {
            console.error('Error submitting questions:', error);
            showNotification('Error: ' + error.message, 'error');
        } finally {
            // Restore button state with animation
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                submitBtn.style.opacity = '1';
            }, 500);
        }
    });

    // Auto-resize textareas with smooth animation
    document.addEventListener('input', function(e) {
        if (e.target.tagName === 'TEXTAREA') {
            e.target.style.height = 'auto';
            const newHeight = e.target.scrollHeight + 'px';
            e.target.style.height = newHeight;

            // Add subtle glow effect when typing
            e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1), 0 10px 25px -5px rgba(0, 0, 0, 0.1)';

            // Remove glow after a delay
            clearTimeout(e.target.glowTimeout);
            e.target.glowTimeout = setTimeout(() => {
                e.target.style.boxShadow = '';
            }, 1000);
        }
    });

    // Add intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe feature cards for scroll animations
    document.querySelectorAll('.bg-white\\/70, .backdrop-blur-xl').forEach(card => {
        // Skip if it's the main form card to avoid conflicts
        if (!card.closest('#faqForm')) {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        }
    });

    // Form validation
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('border-red-500')) {
                validateField(this);
            }
        });
    });

    function validateField(field) {
        const isValid = field.checkValidity();
        
        if (isValid) {
            field.classList.remove('error');
            field.classList.add('border-green-500');
        } else {
            field.classList.remove('border-green-500');
            field.classList.add('error');
        }
    }

    // Email validation
    const emailInput = document.getElementById('userEmail');
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.setCustomValidity('Please enter a valid email address');
        } else {
            this.setCustomValidity('');
        }
    });

    // Phone number formatting (optional)
    const phoneInput = document.getElementById('userPhone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        this.value = value;
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add some interactive animations
    function addQuestionItemAnimations() {
        const questionItems = document.querySelectorAll('.question-item');
        questionItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '';
            });
        });
    }

    // Initial call
    addQuestionItemAnimations();

    // Add floating label effect for inputs
    const allInputs = document.querySelectorAll('input, textarea');
    allInputs.forEach(input => {
        input.addEventListener('focus', function() {
            const label = this.previousElementSibling;
            if (label && label.tagName === 'LABEL') {
                label.style.transform = 'translateY(-2px)';
                label.style.color = '#3b82f6';
            }
        });

        input.addEventListener('blur', function() {
            const label = this.previousElementSibling;
            if (label && label.tagName === 'LABEL') {
                label.style.transform = 'translateY(0)';
                label.style.color = '';
            }
        });
    });

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-xl shadow-2xl transform translate-x-full transition-all duration-300 ${
            type === 'error' ? 'bg-red-50 border border-red-200 text-red-800' :
            type === 'success' ? 'bg-emerald-50 border border-emerald-200 text-emerald-800' :
            'bg-blue-50 border border-blue-200 text-blue-800'
        }`;

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'success' ? 'fa-check-circle' :
                    'fa-info-circle'
                } mr-3"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }
});
