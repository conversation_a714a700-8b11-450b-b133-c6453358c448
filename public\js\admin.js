// Modern Admin Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const logoutBtn = document.getElementById('logoutBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const exportBtn = document.getElementById('exportBtn');
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const questionsTableBody = document.getElementById('questionsTableBody');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const questionModal = document.getElementById('questionModal');
    const closeModal = document.getElementById('closeModal');
    const modalContent = document.getElementById('modalContent');

    // Add entrance animations
    const cards = document.querySelectorAll('.bg-white\\/70, .backdrop-blur-xl');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Stats elements
    const totalQuestions = document.getElementById('totalQuestions');
    const pendingQuestions = document.getElementById('pendingQuestions');
    const answeredQuestions = document.getElementById('answeredQuestions');
    const uniqueUsers = document.getElementById('uniqueUsers');

    let allQuestions = [];
    let filteredQuestions = [];

    // Initialize dashboard
    init();

    async function init() {
        await loadQuestions();
        setupEventListeners();
    }

    // Event listeners
    function setupEventListeners() {
        logoutBtn.addEventListener('click', handleLogout);
        refreshBtn.addEventListener('click', handleRefresh);
        exportBtn.addEventListener('click', handleExport);
        searchInput.addEventListener('input', handleSearch);
        statusFilter.addEventListener('change', handleFilter);
        clearFiltersBtn.addEventListener('click', clearFilters);
        closeModal.addEventListener('click', closeQuestionModal);

        // Close modal on outside click
        questionModal.addEventListener('click', function(e) {
            if (e.target === questionModal) {
                closeQuestionModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeQuestionModal();
            }
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                handleRefresh();
            }
        });
    }

    // Load questions from server
    async function loadQuestions() {
        try {
            showLoading();
            
            const response = await fetch('/api/admin/questions');
            const result = await response.json();

            if (result.success) {
                allQuestions = result.questions;
                filteredQuestions = [...allQuestions];
                updateStats();
                renderQuestions();
            } else {
                throw new Error(result.message || 'Failed to load questions');
            }
        } catch (error) {
            console.error('Error loading questions:', error);
            showError('Failed to load questions: ' + error.message);
        } finally {
            hideLoading();
        }
    }

    // Update statistics with animations
    function updateStats() {
        const total = allQuestions.length;
        const pending = allQuestions.filter(q => q.status === 'pending').length;
        const answered = allQuestions.filter(q => q.status === 'answered').length;
        const users = new Set(allQuestions.map(q => q.user_email)).size;

        animateNumber(totalQuestions, total);
        animateNumber(pendingQuestions, pending);
        animateNumber(answeredQuestions, answered);
        animateNumber(uniqueUsers, users);
    }

    // Animate number counting with modern easing
    function animateNumber(element, target) {
        const start = parseInt(element.textContent) || 0;
        const duration = 1500;
        const startTime = performance.now();

        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (target - start) * easeOutQuart);

            element.textContent = current;

            // Add subtle scale effect during animation
            const scale = 1 + (Math.sin(progress * Math.PI) * 0.05);
            element.style.transform = `scale(${scale})`;

            if (progress < 1) {
                requestAnimationFrame(update);
            } else {
                element.style.transform = 'scale(1)';
            }
        }

        requestAnimationFrame(update);
    }

    // Render questions table
    function renderQuestions() {
        if (filteredQuestions.length === 0) {
            questionsTableBody.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }

        emptyState.classList.add('hidden');
        
        questionsTableBody.innerHTML = filteredQuestions.map(question => `
            <tr class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">${question.user_name.charAt(0).toUpperCase()}</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${escapeHtml(question.user_name)}</div>
                            <div class="text-sm text-gray-500">${escapeHtml(question.user_email)}</div>
                            ${question.user_company ? `<div class="text-xs text-gray-400">${escapeHtml(question.user_company)}</div>` : ''}
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 max-w-xs truncate" title="${escapeHtml(question.question_text)}">
                        ${escapeHtml(question.question_text)}
                    </div>
                    <button onclick="viewQuestion(${question.id})" class="text-blue-600 hover:text-blue-800 text-xs mt-1">
                        View full question →
                    </button>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <select onchange="updateStatus(${question.id}, this.value)" 
                        class="status-badge status-${question.status} border-0 bg-transparent cursor-pointer">
                        <option value="pending" ${question.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="reviewed" ${question.status === 'reviewed' ? 'selected' : ''}>Reviewed</option>
                        <option value="answered" ${question.status === 'answered' ? 'selected' : ''}>Answered</option>
                        <option value="archived" ${question.status === 'archived' ? 'selected' : ''}>Archived</option>
                    </select>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${formatDate(question.created_at)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="viewQuestion(${question.id})" 
                        class="text-blue-600 hover:text-blue-900 mr-3" title="View">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="deleteQuestion(${question.id})" 
                        class="text-red-600 hover:text-red-900" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // Search functionality
    function handleSearch() {
        const query = searchInput.value.toLowerCase();
        applyFilters(query, statusFilter.value);
    }

    // Filter functionality
    function handleFilter() {
        const query = searchInput.value.toLowerCase();
        applyFilters(query, statusFilter.value);
    }

    // Apply filters
    function applyFilters(searchQuery, statusValue) {
        filteredQuestions = allQuestions.filter(question => {
            const matchesSearch = !searchQuery || 
                question.user_name.toLowerCase().includes(searchQuery) ||
                question.user_email.toLowerCase().includes(searchQuery) ||
                question.question_text.toLowerCase().includes(searchQuery) ||
                (question.user_company && question.user_company.toLowerCase().includes(searchQuery));
            
            const matchesStatus = !statusValue || question.status === statusValue;
            
            return matchesSearch && matchesStatus;
        });
        
        renderQuestions();
    }

    // Clear filters
    function clearFilters() {
        searchInput.value = '';
        statusFilter.value = '';
        filteredQuestions = [...allQuestions];
        renderQuestions();
    }

    // Handle logout
    async function handleLogout() {
        if (confirm('Are you sure you want to logout?')) {
            try {
                await fetch('/api/admin/logout', { method: 'POST' });
                window.location.href = '/admin';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = '/admin';
            }
        }
    }

    // Handle refresh with modern animation
    async function handleRefresh() {
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div><span>Refreshing...</span>';
        refreshBtn.disabled = true;
        refreshBtn.style.transform = 'scale(0.98)';

        await loadQuestions();

        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
            refreshBtn.style.transform = 'scale(1)';
        }, 500);
    }

    // Handle export with modern animation
    async function handleExport() {
        try {
            const originalText = exportBtn.innerHTML;
            exportBtn.disabled = true;
            exportBtn.innerHTML = '<div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div><span>Exporting...</span>';
            exportBtn.style.transform = 'scale(0.98)';

            const response = await fetch('/api/admin/export');

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `winplus-faq-${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Success animation
                exportBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Exported!';
                exportBtn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                setTimeout(() => {
                    exportBtn.innerHTML = originalText;
                    exportBtn.style.background = '';
                }, 2000);
            } else {
                throw new Error('Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            showError('Failed to export questions');
        } finally {
            setTimeout(() => {
                exportBtn.disabled = false;
                exportBtn.style.transform = 'scale(1)';
            }, 500);
        }
    }

    // Global functions for inline event handlers
    window.updateStatus = async function(questionId, newStatus) {
        try {
            const response = await fetch(`/api/admin/questions/${questionId}/status`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: newStatus })
            });

            const result = await response.json();
            if (result.success) {
                // Update local data
                const question = allQuestions.find(q => q.id === questionId);
                if (question) {
                    question.status = newStatus;
                    updateStats();
                    applyFilters(searchInput.value.toLowerCase(), statusFilter.value);
                }
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Error updating status:', error);
            alert('Failed to update status');
            await loadQuestions(); // Reload to reset
        }
    };

    window.deleteQuestion = async function(questionId) {
        if (confirm('Are you sure you want to delete this question?')) {
            try {
                const response = await fetch(`/api/admin/questions/${questionId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                if (result.success) {
                    await loadQuestions(); // Reload questions
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Error deleting question:', error);
                alert('Failed to delete question');
            }
        }
    };

    window.viewQuestion = function(questionId) {
        const question = allQuestions.find(q => q.id === questionId);
        if (question) {
            modalContent.innerHTML = `
                <div class="space-y-6">
                    <div class="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">${question.user_name.charAt(0).toUpperCase()}</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">${escapeHtml(question.user_name)}</h4>
                            <p class="text-sm text-gray-600">${escapeHtml(question.user_email)}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        ${question.user_phone ? `
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">Phone</label>
                                <p class="mt-1 text-sm text-gray-900">${escapeHtml(question.user_phone)}</p>
                            </div>
                        ` : ''}
                        ${question.user_company ? `
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide">Company</label>
                                <p class="mt-1 text-sm text-gray-900">${escapeHtml(question.user_company)}</p>
                            </div>
                        ` : ''}
                    </div>

                    <div>
                        <label class="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Question</label>
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
                            <p class="text-gray-900 leading-relaxed">${escapeHtml(question.question_text)}</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex items-center space-x-2">
                            <span class="text-xs font-medium text-gray-500 uppercase tracking-wide">Status:</span>
                            <span class="status-badge status-${question.status}">${question.status}</span>
                        </div>
                        <div class="text-xs text-gray-500">
                            Submitted ${formatDate(question.created_at)}
                        </div>
                    </div>
                </div>
            `;

            // Animate modal in
            questionModal.classList.remove('hidden');
            const modalCard = questionModal.querySelector('.bg-white');
            modalCard.style.opacity = '0';
            modalCard.style.transform = 'scale(0.95)';

            setTimeout(() => {
                modalCard.style.opacity = '1';
                modalCard.style.transform = 'scale(1)';
            }, 50);
        }
    };

    function closeQuestionModal() {
        questionModal.classList.add('hidden');
    }

    // Utility functions
    function showLoading() {
        loadingState.classList.remove('hidden');
        emptyState.classList.add('hidden');
    }

    function hideLoading() {
        loadingState.classList.add('hidden');
    }

    function showError(message) {
        showNotification(message, 'error');
    }

    function showSuccess(message) {
        showNotification(message, 'success');
    }

    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-xl shadow-2xl transform translate-x-full transition-all duration-300 ${
            type === 'error' ? 'bg-red-50 border border-red-200 text-red-800' :
            type === 'success' ? 'bg-emerald-50 border border-emerald-200 text-emerald-800' :
            'bg-blue-50 border border-blue-200 text-blue-800'
        }`;

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'success' ? 'fa-check-circle' :
                    'fa-info-circle'
                } mr-3"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
});
