# Winplus FAQ System - Setup Guide

## Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Start the Server
```bash
npm start
```

### 3. Access the Application
- **Main FAQ Page**: http://localhost:3000
- **Admin Dashboard**: http://localhost:3000/admin

### 4. Admin Login Credentials
- **Username**: `admin`
- **Password**: `Sophatel@9999`

## Development

### Running in Development Mode
```bash
npm run dev
```

### File Structure
- `public/` - Static files (HTML, CSS, JS)
- `server.js` - Express.js server
- `database.js` - SQLite database handler
- `faq_database.sqlite` - SQLite database file (auto-created)

### Making Changes
1. Edit files in the `public/` directory for frontend changes
2. Edit `server.js` for backend API changes
3. Edit `database.js` for database schema changes
4. Restart the server after backend changes

## Customization

### Changing Colors
Edit the CSS variables in `public/css/styles.css`:
```css
:root {
  --primary-500: #0ea5e9;  /* Main blue color */
  --primary-600: #0284c7;  /* Darker blue */
  /* ... other colors */
}
```

### Adding New Fields
1. Update the HTML forms in `public/index.html`
2. Update the JavaScript in `public/js/main.js`
3. Update the database schema in `database.js`
4. Update the server endpoints in `server.js`

### Changing Admin Credentials
Edit the credentials in `server.js`:
```javascript
const ADMIN_CREDENTIALS = {
    username: 'your_username',
    password: 'your_password'
};
```

## Deployment

### Production Setup
1. Set environment variables:
   ```bash
   export NODE_ENV=production
   export PORT=3000
   ```

2. Enable HTTPS and update session config:
   ```javascript
   cookie: { 
       secure: true,  // Enable for HTTPS
       maxAge: 24 * 60 * 60 * 1000
   }
   ```

3. Use a process manager like PM2:
   ```bash
   npm install -g pm2
   pm2 start server.js --name "winplus-faq"
   ```

### Database Backup
The SQLite database file `faq_database.sqlite` contains all your data. 
Make regular backups of this file.

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill process on port 3000
   npx kill-port 3000
   ```

2. **Database locked**
   - Restart the server
   - Check if another process is using the database

3. **CSS not loading**
   - Clear browser cache
   - Check file paths in HTML

4. **JavaScript errors**
   - Check browser console for errors
   - Ensure all script files are loaded correctly

### Logs
Check the server console for error messages and debugging information.

## Support
For issues or questions about the Winplus FAQ system, check the console logs and ensure all dependencies are properly installed.
